#!/bin/bash
set -e

echo "🚀 Setting up SpatialTracker V2..."

# 1. Clone repository if not already cloned
if [ ! -d "SpaTrackerV2" ]; then
    echo "📥 Cloning repository..."
    git clone https://github.com/henry123-boy/SpaTrackerV2.git
fi

cd SpaTrackerV2

# 2. Download example data
echo "📦 Downloading example data..."
git submodule update --init --recursive

# 3. Create conda environment
echo "🐍 Creating conda environment..."
conda create -n SpaTrack2 python=3.11 -y
source activate SpaTrack2

# 4. Install PyTorch
echo "🔥 Installing PyTorch..."
python -m pip install torch==2.4.1 torchvision==0.19.1 torchaudio==2.4.1 --index-url https://download.pytorch.org/whl/cu124

# 5. Install requirements
echo "📋 Installing requirements..."
python -m pip install -r requirements.txt

# 6. Install Gradio
echo "🎨 Installing Gradio..."
python -m pip install gradio==5.31.0 pako

# 7. Setup SAM
echo "🎯 Setting up SAM..."
python -m pip install git+https://github.com/facebookresearch/segment-anything.git

# Create SAM checkpoints directory
mkdir -p app_3rd/sam_utils/checkpoints
cd app_3rd/sam_utils/checkpoints

# Download SAM checkpoint
echo "⬇️ Downloading SAM checkpoint..."
wget https://dl.fbaipublicfiles.com/segment_anything/sam_vit_h_4b8939.pth

# Go back to root
cd ../../..

# 8. Download example data for RGBD demo
echo "📊 Downloading example data..."
bash scripts/download.sh

# 9. Launch the interactive interface
echo "🌟 Launching SpatialTracker V2 Interactive Interface..."
echo "🔗 Opening at http://localhost:7860"
echo "✨ You can now upload videos and start tracking!"

python app.py