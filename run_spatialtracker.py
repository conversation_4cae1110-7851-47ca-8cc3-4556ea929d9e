#!/usr/bin/env python3
import subprocess
import sys
import os
from pathlib import Path

def run_command(cmd, description):
    """Run a command and handle errors"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(cmd, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e}")
        print(f"Error output: {e.stderr}")
        return False

def main():
    print("🚀 SpatialTracker V2 Setup and Launch Script")
    
    # Check if we're in the right directory
    if not Path("app.py").exists():
        if Path("SpaTrackerV2/app.py").exists():
            os.chdir("SpaTrackerV2")
        else:
            print("❌ Please run this from the SpaTrackerV2 directory or its parent")
            return
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ required")
        return
    
    print("📋 Installing requirements...")
    run_command("pip install -r requirements.txt", "Installing requirements")
    
    print("🎨 Installing Gradio...")
    run_command("pip install gradio==5.31.0 pako", "Installing Gradio")
    
    print("🎯 Installing SAM...")
    run_command("pip install git+https://github.com/facebookresearch/segment-anything.git", "Installing SAM")
    
    # Create SAM checkpoint directory
    sam_dir = Path("app_3rd/sam_utils/checkpoints")
    sam_dir.mkdir(parents=True, exist_ok=True)
    
    # Download SAM checkpoint if not exists
    sam_checkpoint = sam_dir / "sam_vit_h_4b8939.pth"
    if not sam_checkpoint.exists():
        print("⬇️ Downloading SAM checkpoint...")
        run_command(f"wget -O {sam_checkpoint} https://dl.fbaipublicfiles.com/segment_anything/sam_vit_h_4b8939.pth", "Downloading SAM checkpoint")
    
    # Download example data if script exists
    if Path("scripts/download.sh").exists():
        print("📊 Downloading example data...")
        run_command("bash scripts/download.sh", "Downloading example data")
    
    print("\n🌟 Launching SpatialTracker V2...")
    print("🔗 Interface will be available at: http://localhost:7860")
    print("✨ Upload a video and start tracking!")
    print("\n" + "="*50)
    
    # Launch the app
    try:
        subprocess.run([sys.executable, "app.py"], check=True)
    except KeyboardInterrupt:
        print("\n👋 SpatialTracker V2 stopped")
    except Exception as e:
        print(f"❌ Failed to launch: {e}")

if __name__ == "__main__":
    main()